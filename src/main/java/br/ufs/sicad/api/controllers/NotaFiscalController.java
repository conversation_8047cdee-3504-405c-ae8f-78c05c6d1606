package br.ufs.sicad.api.controllers;

import br.ufs.sicad.api.dtos.NotaFiscalDTO;
import br.ufs.sicad.api.forms.DocumentoForm;
import br.ufs.sicad.api.forms.NotaFiscalForm;
import br.ufs.sicad.api.forms.NotaFiscalUpdateForm;
import br.ufs.sicad.domain.entidades.Documento;
import br.ufs.sicad.domain.entidades.NotaFiscal;
import br.ufs.sicad.services.NotaFiscalService;
import br.ufs.sicad.utils.Utils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

@RestController
@RequestMapping("/notas-fiscais")
@RequiredArgsConstructor
@Tag(name = "Notas Fiscais", description = "Endpoints para gerenciamento de Notas Fiscais")
public class NotaFiscalController {

    private final NotaFiscalService notaFiscalService;


    @GetMapping
    @Operation(summary = "Listar notas fiscais com filtros opcionais")
    public ResponseEntity<Page<NotaFiscalDTO>> listarNotasFiscais(
            @RequestParam(required = false) String numero,
            @RequestParam(required = false) String statusPagamento,
            @RequestParam(required = false) String dataEmissao,
            @Parameter(hidden = true) Pageable pageable) {
        
        LocalDate dataEmissaoConvertida = Utils.converter(dataEmissao);
        Page<NotaFiscal> notasFiscais = notaFiscalService.listarNotasFiscais(
                numero, statusPagamento, dataEmissaoConvertida, pageable);
        Page<NotaFiscalDTO> dtos = notasFiscais.map(NotaFiscalDTO::from);
        return ResponseEntity.ok(dtos);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Buscar nota fiscal por ID")
    public ResponseEntity<NotaFiscalDTO> buscarNotaFiscalPorId(@PathVariable Long id) {
        NotaFiscal notaFiscal = notaFiscalService.buscarNotaFiscalPor(id);
        return ResponseEntity.ok(NotaFiscalDTO.from(notaFiscal));
    }

    @PostMapping
    @Operation(summary = "Criar nova nota fiscal")
    public ResponseEntity<NotaFiscalDTO> criarNotaFiscal(@RequestBody @Valid NotaFiscalForm form) {
        NotaFiscal notaFiscal = form.asNotaFiscal();
        
        Documento documento = null;
        if (form.documento() != null) {
            documento = form.documento().asDocumento();
        }
        
        NotaFiscal notaFiscalSalva = notaFiscalService.criarNotaFiscal(
                notaFiscal,
                form.notaEmpenhoId(),
                form.contratoId(),
                form.itensEntregues(),
                documento
        );
        
        return ResponseEntity.status(HttpStatus.CREATED).body(NotaFiscalDTO.from(notaFiscalSalva));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Atualizar nota fiscal")
    public ResponseEntity<NotaFiscalDTO> atualizarNotaFiscal(
            @PathVariable Long id,
            @RequestBody @Valid NotaFiscalUpdateForm form) {
        
        NotaFiscal notaFiscalAtualizada = notaFiscalService.atualizarNotaFiscal(
                id,
                form.numero(),
                Utils.converter(form.dataEmissao()),
                form.valor(),
                form.statusPagamento(),
                form.itensEntregues()
        );
        
        return ResponseEntity.ok(NotaFiscalDTO.from(notaFiscalAtualizada));
    }

    @PostMapping("/{id}/marcar-como-paga")
    @Operation(summary = "Marcar nota fiscal como paga")
    public ResponseEntity<NotaFiscalDTO> marcarComoPaga(@PathVariable Long id) {
        NotaFiscal notaFiscal = notaFiscalService.marcarComoPaga(id);
        return ResponseEntity.ok(NotaFiscalDTO.from(notaFiscal));
    }


    @PostMapping("/{id}/anexar-documento")
    @Operation(summary = "Anexar documento à nota fiscal")
    public ResponseEntity<NotaFiscalDTO> anexarDocumento(
            @PathVariable Long id,
            @RequestBody @Valid DocumentoForm documentoForm) {
        
        Documento documento = documentoForm.asDocumento();
        NotaFiscal notaFiscal = notaFiscalService.anexarArquivo(id, documento);
        return ResponseEntity.ok(NotaFiscalDTO.from(notaFiscal));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Excluir nota fiscal")
    public ResponseEntity<Void> excluirNotaFiscal(@PathVariable Long id) {
        notaFiscalService.excluirNotaFiscal(id);
        return ResponseEntity.noContent().build();
    }
}
