package br.ufs.sicad.api.forms;

import br.ufs.sicad.domain.entidades.NotaFiscal;
import br.ufs.sicad.domain.enums.StatusPagamento;
import br.ufs.sicad.utils.Utils;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

import java.math.BigDecimal;
import java.util.Map;

public record NotaFiscalForm(
        @NotBlank(message = "O número da nota fiscal é obrigatório")
        String numero,
        
        @NotNull(message = "A data de emissão é obrigatória")
        @JsonFormat(pattern = "dd/MM/yyyy")
        String dataEmissao,
        
        @NotNull(message = "O valor é obrigatório")
        @Positive(message = "O valor deve ser positivo")
        BigDecimal valor,
        
        StatusPagamento statusPagamento,
        
        @NotNull(message = "O ID da nota de empenho é obrigatório")
        Long notaEmpenhoId,
        
        @NotNull(message = "O ID do contrato é obrigatório")
        Long contratoId,
        
        // Mapa de itens entregues: Item ID -> Quantidade
        Map<Long, Integer> itensEntregues,
        
        // Dados do documento (opcional na criação)
        DocumentoForm documento
) {
    public NotaFiscal asNotaFiscal() {
        NotaFiscal notaFiscal = new NotaFiscal();
        notaFiscal.setNumero(numero);
        notaFiscal.setDataEmissao(Utils.converter(dataEmissao));
        notaFiscal.setValor(valor);
        notaFiscal.setStatusPagamento(statusPagamento != null ? statusPagamento : StatusPagamento.PENDENTE);
        return notaFiscal;
    }
}
